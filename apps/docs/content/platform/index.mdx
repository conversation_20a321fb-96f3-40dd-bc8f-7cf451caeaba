---
title: "Introduction"

mode: "center"

icon: Album

---

import { Accordion, Accordions } from 'fumadocs-ui/components/accordion';

import { Tabs } from 'fumadocs-ui/components/tabs';

import { Platinum } from '../../components/platinum';

import { Gold } from '../../components/gold';

import { Silver } from '../../components/silver';

import { Bronze } from '../../components/bronze';

<div className="flex flex-col items-stretch text-center">

<div style={{height:"170px"}}>

<img className="block dark:hidden mx-auto my-0! h-full" alt="Libra logo" src="/logo.svg" />

<img className="hidden dark:block mx-auto my-0! h-full" alt="Libra logo" src="/logo-dark.svg" />

</div>

<h1 className="text-3xl font-bold">Libra</h1>

<p className="mt-2 mb-1"></p>

<div className="flex flex-row flex-wrap justify-center gap-1 py-2">

<a className="border-none" href="https://github.com/saasfly/libra/actions/workflows/check.yml">

<img className="h-[20px] m-0!" src="https://img.shields.io/github/actions/workflow/status/saasfly/libra/ci.yml?label=ci" alt="GitHub Actions Workflow Status" />

</a>

<a className="border-none" href="https://github.com/saasfly/libra/blob/main/LICENSE">

<img className="h-[20px] m-0!" src="https://img.shields.io/badge/License-AGPL-green.svg" alt="GitHub License" />

</a>



<a className="border-none" href="https://x.com/nextify2024">

<img className="h-[20px] m-0!" src="https://img.shields.io/badge/made_by-nextify2024-blue?color=FF782B&link=https://x.com/nextify2024" alt="Made by Nextify2024" />

</a>

<a className="border-none" href="https://github.com/saasfly/libra" rel="nofollow">

<img className="h-[20px] m-0!" src="https://img.shields.io/github/stars/saasfly/libra" alt="stars" />

</a>

</div>

<div className="flex flex-row justify-center gap-1">

<a href="https://libra.dev">Official Site</a>

<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>



<a href="https://x.com/nextify2024">𝕏</a>

<span>&nbsp;&nbsp;•&nbsp;&nbsp;</span>

<br />

</div>

</div>

## Introduction

Libra AI is an intelligent development platform that enables users of different technical backgrounds to easily build full-stack web applications.

You just need to describe your ideas in natural language, and Libra can instantly transform your ideas into fully functional web applications and websites.

No programming experience is required - experience a revolutionary development approach that allows you to go from concept to application deployment at unprecedented speed.

## Core Features

- **AI Intelligent Development**: Transform natural language descriptions into fully functional applications

- **Zero-Code Development**: Build complex web applications without writing any code

- **Full-Stack Development Capability**: Create both front-end interfaces and back-end functionality simultaneously

- **Real-Time Preview**: View application effects in real-time during development

- **Modern Technology Stack**: Based on the latest web technologies and industry best practices

- **One-Click Deployment**: Quickly deploy your applications to production environments

- **Team Collaboration**: Collaborate efficiently with team members in a unified workspace

- **Extensible Integrations**: Standard interfaces ready for connecting with mainstream development tools and third-party services

## Application Scenarios

Using Libra, you can build various types of websites and JavaScript-based full-stack web applications. You can check out our application showcase to understand actual cases that have been successfully developed and released through Libra.

Supported application types include:

- **Content Websites**: Personal blogs, portfolio showcases, corporate websites, and technical documentation

- **E-commerce Platforms**: Online stores with integrated payment processing and inventory management

- **Productivity Tools**: Task management systems, note-taking applications, and team collaboration platforms

- **Social Applications**: Community forums, communication platforms, and social networks

- **Enterprise Applications**: Customer relationship management systems, data dashboards, and business analytics platforms

- **Education Platforms**: Online learning systems and course management websites

### Technical Ecosystem

Libra is deeply integrated with the modern web development tool ecosystem:

**Design and Prototyping**

- Figma: Supports design collaboration and resource import (coming soon)

**Deployment and Hosting Services**

- Cloudflare: Provides edge deployment and performance optimization

**Back-end Services**

- More integrations coming soon

**Development and Collaboration Tools**

- GitHub: Supports version control, code backup, and team collaboration (coming soon)

**Payment Processing Services**

- Stripe: Provides secure payment processing and subscription management (coming soon)

Check out our [Technical Support](/docs/technologies) guide to learn about the complete technology stack and recommended architecture patterns.

## Target Audience

Libra is committed to helping users build various applications, from simple content websites (such as personal blogs) to complex full-stack web applications (such as enterprise-level productivity tools). The platform requires users to be able to clearly express their needs, and having some understanding of web development concepts will be helpful.

### Getting Started with Zero Foundation

You don't need to have a programming foundation to get started. Developing applications with Libra is an excellent way to learn web development through a practical, hands-on approach, with AI assistance guiding you throughout the development process.

### Key Elements for Successful Development

To fully leverage Libra's capabilities, it's recommended that you clearly define the following:

- **Development Goals**: Clearly describe the purpose and core functionality of your application

- **User Experience Design**: Define how users will interact with your application

- **Success Criteria**: Define specific criteria for project completion

- **Design Requirements**: Determine visual style, page layout, and branding elements

We recommend reading our [Best Practices Guide](/docs/best-practices) to learn how to efficiently collaborate with AI and master communication skills in different development modes.

## Technical Architecture and Ecosystem

Libra is built on modern web technologies and seamlessly integrates with a wide range of development ecosystems. The platform's core technologies include:

- **Cutting-Edge AI Models**: Utilizes state-of-the-art large language models for intelligent code generation

- **Cloud-Native Infrastructure**: Provides highly scalable and reliable hosting services

- **Mainstream Development Frameworks**: Deeply integrates with popular front-end frameworks such as React and Next.js

- **Complete Development Toolchain**: Provides an integrated development environment and team collaboration features

## Pricing Plans

Libra offers flexible pricing plans to meet the needs of different users and use cases. Our pricing strategy is transparent and clear, supporting elastic scaling:

- **Free Plan**: Suitable for learning and small project development

- **Professional Plan**: Provides advanced features and more resources for professional developers

- **MAX Plan**: Unlimited access to all platform features

- **Enterprise Plan**: Customized solutions for large organizations

Pricing is based on actual usage and feature access. Learn more about [usage efficiency](/docs/tokens) and [detailed pricing](/pricing) information.

<br/>

## Community and Support

Join our rapidly growing community of developers and creators:

- **GitHub Community**: Participate in open-source project contributions and provide feedback



- **Comprehensive Documentation**: Provides comprehensive usage guides and development tutorials

{/* - **Video Tutorials**: Step-by-step walkthroughs and use cases */}

## Sponsorship and Support

We sincerely appreciate the support of all our sponsors, as it is your help that enables Libra AI to continue to grow. We welcome sponsorships at any level, as every contribution will help us continue to improve and refine the platform.

If you've built successful products with Libra or wish to support open-source AI development, we invite you to join our [corporate sponsorship program](https://github.com/sponsors/saasfly).

### Sponsorship Tiers

Our sponsorship program has multiple tiers, creating value for sponsors while driving the growth of the Libra ecosystem:

#### Platinum Tier - $2,000+/month

Enjoy top-tier visibility with exclusive showcase space, detailed corporate introduction, and maximum exposure across all Libra platforms.

<Platinum />

<br/>

#### Gold Tier - $500+/month

Get featured visibility with corporate logo, detailed introduction, and prominent placement in the sponsor showcase area.

<Gold />

<br/>

#### Silver Tier - $100+/month

Display your corporate logo in the sponsor showcase area with a direct link to your official website or product page.

<Silver locale="en" />

<br/>

#### Bronze Tier - $25+/month

Get recognized in the community sponsor section with your corporate logo and official website link.

<Bronze locale="en" />

<br/>

### Sponsor Benefits

- **Community Recognition**: Get special recognition in official documentation and website

- **GitHub Sponsors Badge**: Display your official sponsor status on your GitHub profile

- **Priority Technical Support**: Enjoy faster technical support response times

- **Early Access to New Features**: Get priority access to upcoming new features and provide valuable feedback

- **Priority Technical Support**: Enjoy faster technical support response times

Ready to become a sponsor? Visit our [GitHub Sponsors page](https://github.com/sponsors/saasfly) to start your sponsorship journey.